# DEHA Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying DEHA (Dynamic Evolutionary Hybrid Architecture) models in production environments. It covers deployment strategies, infrastructure requirements, monitoring setup, and operational procedures for enterprise-grade AI model deployment.

## Table of Contents

1. [Deployment Architecture](#deployment-architecture)
2. [Infrastructure Requirements](#infrastructure-requirements)
3. [Container Orchestration](#container-orchestration)
4. [Cloud Deployment](#cloud-deployment)
5. [Edge Deployment](#edge-deployment)
6. [Monitoring and Observability](#monitoring-and-observability)
7. [Security Configuration](#security-configuration)
8. [Scaling Strategies](#scaling-strategies)
9. [Disaster Recovery](#disaster-recovery)
10. [Operational Procedures](#operational-procedures)

## Deployment Architecture

### Production Architecture Overview

```mermaid
graph TB
    subgraph "Load Balancer Tier"
        LB[Load Balancer]
        WAF[Web Application Firewall]
    end
    
    subgraph "API Gateway Tier"
        GW1[API Gateway 1]
        GW2[API Gateway 2]
        GW3[API Gateway 3]
    end
    
    subgraph "Application Tier"
        APP1[DEHA Service 1]
        APP2[DEHA Service 2]
        APP3[DEHA Service 3]
    end
    
    subgraph "Memory Tier"
        VECTOR[Vector DB Cluster]
        GRAPH[Knowledge Graph Cluster]
        CACHE[Redis Cluster]
    end
    
    subgraph "Storage Tier"
        S3[Object Storage]
        DB[Metadata DB]
    end
    
    subgraph "Monitoring Tier"
        PROM[Prometheus]
        GRAF[Grafana]
        ALERT[AlertManager]
    end
    
    WAF --> LB
    LB --> GW1
    LB --> GW2
    LB --> GW3
    GW1 --> APP1
    GW2 --> APP2
    GW3 --> APP3
    APP1 --> VECTOR
    APP2 --> GRAPH
    APP3 --> CACHE
    APP1 --> S3
    APP2 --> DB
    PROM --> GRAF
    PROM --> ALERT
```

### Deployment Patterns

#### 1. Blue-Green Deployment
```yaml
# Blue-Green deployment configuration
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: deha-model-service
spec:
  replicas: 5
  strategy:
    blueGreen:
      activeService: deha-active
      previewService: deha-preview
      autoPromotionEnabled: false
      scaleDownDelaySeconds: 30
      prePromotionAnalysis:
        templates:
        - templateName: success-rate
        args:
        - name: service-name
          value: deha-preview
      postPromotionAnalysis:
        templates:
        - templateName: success-rate
        args:
        - name: service-name
          value: deha-active
  selector:
    matchLabels:
      app: deha-model
  template:
    metadata:
      labels:
        app: deha-model
    spec:
      containers:
      - name: deha-model
        image: deha/model-service:{{.Values.image.tag}}
        ports:
        - containerPort: 8000
```

#### 2. Canary Deployment
```yaml
# Canary deployment with traffic splitting
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: deha-canary
spec:
  replicas: 10
  strategy:
    canary:
      steps:
      - setWeight: 10
      - pause: {duration: 10m}
      - setWeight: 20
      - pause: {duration: 10m}
      - setWeight: 50
      - pause: {duration: 10m}
      - setWeight: 100
      canaryService: deha-canary
      stableService: deha-stable
      trafficRouting:
        istio:
          virtualService:
            name: deha-vs
            routes:
            - primary
```

## Infrastructure Requirements

### Hardware Specifications

#### Production Environment
```yaml
# Minimum production requirements
production:
  compute_nodes:
    count: 3
    cpu: 32 cores
    memory: 128GB RAM
    gpu: 2x A100 80GB
    storage: 2TB NVMe SSD
    network: 25Gbps
  
  storage_nodes:
    count: 3
    cpu: 16 cores
    memory: 64GB RAM
    storage: 10TB SSD
    network: 10Gbps
  
  network:
    bandwidth: 100Gbps
    latency: <1ms intra-cluster
    redundancy: dual-path
```

#### Staging Environment
```yaml
# Staging environment specifications
staging:
  compute_nodes:
    count: 2
    cpu: 16 cores
    memory: 64GB RAM
    gpu: 1x A100 40GB
    storage: 1TB NVMe SSD
    network: 10Gbps
  
  storage_nodes:
    count: 2
    cpu: 8 cores
    memory: 32GB RAM
    storage: 5TB SSD
    network: 10Gbps
```

### Software Requirements

#### Operating System
```bash
# Ubuntu 20.04 LTS configuration
# /etc/os-release
NAME="Ubuntu"
VERSION="20.04.6 LTS (Focal Fossa)"
ID=ubuntu
ID_LIKE=debian
PRETTY_NAME="Ubuntu 20.04.6 LTS"
VERSION_ID="20.04"
```

#### Container Runtime
```bash
# Docker configuration
# /etc/docker/daemon.json
{
  "exec-opts": ["native.cgroupdriver=systemd"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "default-runtime": "nvidia",
  "runtimes": {
    "nvidia": {
      "path": "nvidia-container-runtime",
      "runtimeArgs": []
    }
  }
}
```

#### NVIDIA Container Toolkit
```bash
# Install NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

## Container Orchestration

### Kubernetes Cluster Setup

#### 1. Cluster Configuration
```yaml
# cluster-config.yaml
apiVersion: kubeadm.k8s.io/v1beta3
kind: ClusterConfiguration
kubernetesVersion: v1.28.0
controlPlaneEndpoint: "deha-k8s-lb:6443"
networking:
  serviceSubnet: "10.96.0.0/12"
  podSubnet: "10.244.0.0/16"
apiServer:
  extraArgs:
    enable-admission-plugins: NodeRestriction,ResourceQuota
etcd:
  local:
    dataDir: "/var/lib/etcd"
    extraArgs:
      quota-backend-bytes: "8589934592"  # 8GB
```

#### 2. Node Configuration
```yaml
# node-config.yaml
apiVersion: kubeadm.k8s.io/v1beta3
kind: JoinConfiguration
nodeRegistration:
  kubeletExtraArgs:
    node-labels: "node-type=gpu-worker"
    max-pods: "110"
    kube-reserved: "cpu=1,memory=2Gi"
    system-reserved: "cpu=1,memory=1Gi"
```

#### 3. GPU Operator Installation
```bash
# Install NVIDIA GPU Operator
helm repo add nvidia https://helm.ngc.nvidia.com/nvidia
helm repo update

helm install --wait --generate-name \
  -n gpu-operator --create-namespace \
  nvidia/gpu-operator \
  --set driver.enabled=true \
  --set toolkit.enabled=true \
  --set devicePlugin.enabled=true \
  --set nodeStatusExporter.enabled=true \
  --set gfd.enabled=true \
  --set migManager.enabled=true
```

### Service Mesh Configuration

#### Istio Installation
```bash
# Install Istio
curl -L https://istio.io/downloadIstio | sh -
cd istio-*
export PATH=$PWD/bin:$PATH

# Install with configuration
istioctl install --set values.pilot.traceSampling=1.0 -y

# Enable sidecar injection
kubectl label namespace deha-production istio-injection=enabled
```

#### Service Mesh Configuration
```yaml
# istio-config.yaml
apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: deha-istio
spec:
  values:
    global:
      meshID: deha-mesh
      network: deha-network
    pilot:
      traceSampling: 1.0
      env:
        EXTERNAL_ISTIOD: false
  components:
    pilot:
      k8s:
        resources:
          requests:
            cpu: 500m
            memory: 2048Mi
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
        service:
          type: LoadBalancer
```

## Cloud Deployment

### AWS Deployment

#### 1. EKS Cluster Setup
```bash
# Create EKS cluster with eksctl
eksctl create cluster \
  --name deha-production \
  --version 1.28 \
  --region us-west-2 \
  --nodegroup-name gpu-workers \
  --node-type p4d.24xlarge \
  --nodes 3 \
  --nodes-min 1 \
  --nodes-max 10 \
  --managed \
  --with-oidc \
  --ssh-access \
  --ssh-public-key deha-key
```

#### 2. AWS Load Balancer Controller
```bash
# Install AWS Load Balancer Controller
helm repo add eks https://aws.github.io/eks-charts
helm repo update

helm install aws-load-balancer-controller eks/aws-load-balancer-controller \
  -n kube-system \
  --set clusterName=deha-production \
  --set serviceAccount.create=false \
  --set serviceAccount.name=aws-load-balancer-controller
```

#### 3. Application Load Balancer Configuration
```yaml
# alb-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: deha-alb-ingress
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:************:certificate/********-1234-1234-1234-************
spec:
  rules:
  - host: api.deha.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: deha-api-service
            port:
              number: 80
```

### Azure Deployment

#### 1. AKS Cluster Setup
```bash
# Create resource group
az group create --name deha-rg --location eastus

# Create AKS cluster with GPU nodes
az aks create \
  --resource-group deha-rg \
  --name deha-aks \
  --node-count 3 \
  --node-vm-size Standard_NC24ads_A100_v4 \
  --enable-addons monitoring \
  --generate-ssh-keys \
  --kubernetes-version 1.28.0
```

#### 2. Application Gateway Configuration
```yaml
# azure-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: deha-azure-ingress
  annotations:
    kubernetes.io/ingress.class: azure/application-gateway
    appgw.ingress.kubernetes.io/ssl-redirect: "true"
    appgw.ingress.kubernetes.io/backend-protocol: "http"
spec:
  tls:
  - hosts:
    - api.deha.ai
    secretName: deha-tls-secret
  rules:
  - host: api.deha.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: deha-api-service
            port:
              number: 80
```

### Google Cloud Deployment

#### 1. GKE Cluster Setup
```bash
# Create GKE cluster with GPU nodes
gcloud container clusters create deha-gke \
  --zone us-central1-a \
  --machine-type n1-standard-4 \
  --num-nodes 3 \
  --enable-autoscaling \
  --min-nodes 1 \
  --max-nodes 10 \
  --enable-autorepair \
  --enable-autoupgrade \
  --accelerator type=nvidia-tesla-a100,count=2 \
  --enable-ip-alias
```

#### 2. GPU Driver Installation
```bash
# Install GPU drivers on GKE
kubectl apply -f https://raw.githubusercontent.com/GoogleCloudPlatform/container-engine-accelerators/master/nvidia-driver-installer/cos/daemonset-preloaded.yaml
```

## Edge Deployment

### NVIDIA EGX Platform

#### 1. Edge Node Configuration
```yaml
# edge-node-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-config
data:
  model_path: "/opt/deha/models/deha-7b-quantized"
  max_batch_size: "4"
  max_sequence_length: "2048"
  gpu_memory_fraction: "0.8"
  enable_tensorrt: "true"
```

#### 2. Edge Deployment Manifest
```yaml
# edge-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: deha-edge
spec:
  replicas: 1
  selector:
    matchLabels:
      app: deha-edge
  template:
    metadata:
      labels:
        app: deha-edge
    spec:
      nodeSelector:
        node-type: edge
      containers:
      - name: deha-edge
        image: deha/edge-service:latest
        resources:
          requests:
            nvidia.com/gpu: 1
            memory: "8Gi"
            cpu: "4"
          limits:
            nvidia.com/gpu: 1
            memory: "16Gi"
            cpu: "8"
        env:
        - name: MODEL_PATH
          valueFrom:
            configMapKeyRef:
              name: edge-config
              key: model_path
        - name: ENABLE_TENSORRT
          valueFrom:
            configMapKeyRef:
              name: edge-config
              key: enable_tensorrt
```

### Jetson Deployment

#### 1. Jetson Setup Script
```bash
#!/bin/bash
# jetson-setup.sh

# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# Install NVIDIA Container Runtime
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker

# Install k3s
curl -sfL https://get.k3s.io | sh -
```

#### 2. Jetson Deployment Configuration
```yaml
# jetson-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: deha-jetson
spec:
  replicas: 1
  selector:
    matchLabels:
      app: deha-jetson
  template:
    spec:
      containers:
      - name: deha-jetson
        image: deha/jetson-service:latest
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "8Gi"
            cpu: "4"
        env:
        - name: CUDA_VISIBLE_DEVICES
          value: "0"
        - name: MODEL_OPTIMIZATION
          value: "tensorrt"
```

## Monitoring and Observability

### Prometheus Configuration

#### 1. Prometheus Setup
```yaml
# prometheus-config.yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "deha_rules.yml"

scrape_configs:
  - job_name: 'deha-model-service'
    static_configs:
      - targets: ['deha-model-service:8000']
    metrics_path: /metrics
    scrape_interval: 10s
    
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
```

#### 2. Custom Metrics
```python
# metrics.py
from prometheus_client import Counter, Histogram, Gauge, Info

# Request metrics
REQUEST_COUNT = Counter(
    'deha_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status']
)

REQUEST_LATENCY = Histogram(
    'deha_request_duration_seconds',
    'Request latency in seconds',
    ['endpoint']
)

# Model metrics
MODEL_ACCURACY = Gauge(
    'deha_model_accuracy',
    'Current model accuracy score'
)

ACTIVE_CONNECTIONS = Gauge(
    'deha_active_connections',
    'Number of active connections'
)

GPU_UTILIZATION = Gauge(
    'deha_gpu_utilization_percent',
    'GPU utilization percentage',
    ['gpu_id']
)

MODEL_INFO = Info(
    'deha_model_info',
    'Information about the deployed model'
)
```

### Grafana Dashboards

#### 1. Model Performance Dashboard
```json
{
  "dashboard": {
    "title": "DEHA Model Performance",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(deha_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Latency",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(deha_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "GPU Utilization",
        "type": "graph",
        "targets": [
          {
            "expr": "deha_gpu_utilization_percent",
            "legendFormat": "GPU {{gpu_id}}"
          }
        ]
      }
    ]
  }
}
```

### Alerting Rules

#### 1. Prometheus Alerting Rules
```yaml
# deha_rules.yml
groups:
  - name: deha_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(deha_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"
      
      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(deha_request_duration_seconds_bucket[5m])) > 1.0
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High response latency"
          description: "95th percentile latency is {{ $value }} seconds"
      
      - alert: GPUMemoryHigh
        expr: deha_gpu_memory_used_bytes / deha_gpu_memory_total_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "GPU memory usage high"
          description: "GPU memory usage is {{ $value | humanizePercentage }}"
```

### Distributed Tracing

#### 1. Jaeger Configuration
```yaml
# jaeger-config.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jaeger
  template:
    spec:
      containers:
      - name: jaeger
        image: jaegertracing/all-in-one:latest
        ports:
        - containerPort: 16686
        - containerPort: 14268
        env:
        - name: COLLECTOR_ZIPKIN_HTTP_PORT
          value: "9411"
```

#### 2. Application Tracing
```python
# tracing.py
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

# Configure tracing
trace.set_tracer_provider(TracerProvider())
tracer = trace.get_tracer(__name__)

jaeger_exporter = JaegerExporter(
    agent_host_name="jaeger",
    agent_port=6831,
)

span_processor = BatchSpanProcessor(jaeger_exporter)
trace.get_tracer_provider().add_span_processor(span_processor)

# Usage in application
@tracer.start_as_current_span("model_inference")
def model_inference(query: str):
    with tracer.start_as_current_span("preprocess"):
        processed_query = preprocess(query)
    
    with tracer.start_as_current_span("generate"):
        response = model.generate(processed_query)
    
    return response
```

## Security Configuration

### TLS/SSL Configuration

#### 1. Certificate Management
```yaml
# cert-manager.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
```

#### 2. TLS Certificate
```yaml
# tls-certificate.yaml
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: deha-tls
spec:
  secretName: deha-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - api.deha.ai
  - www.deha.ai
```

### Network Policies

#### 1. Network Segmentation
```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deha-network-policy
spec:
  podSelector:
    matchLabels:
      app: deha-model
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: deha-api-gateway
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: vector-db
    ports:
    - protocol: TCP
      port: 6333
```

### RBAC Configuration

#### 1. Service Account
```yaml
# service-account.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: deha-service-account
  namespace: deha-production
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: deha-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "update"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: deha-role-binding
subjects:
- kind: ServiceAccount
  name: deha-service-account
roleRef:
  kind: Role
  name: deha-role
  apiGroup: rbac.authorization.k8s.io
```

## Scaling Strategies

### Horizontal Pod Autoscaler

#### 1. HPA Configuration
```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: deha-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: deha-model-service
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: deha_active_connections
      target:
        type: AverageValue
        averageValue: "100"
```

### Vertical Pod Autoscaler

#### 1. VPA Configuration
```yaml
# vpa.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: deha-vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: deha-model-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: deha-model
      maxAllowed:
        cpu: "16"
        memory: "64Gi"
      minAllowed:
        cpu: "2"
        memory: "8Gi"
```

### Cluster Autoscaler

#### 1. Cluster Autoscaler Setup
```yaml
# cluster-autoscaler.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cluster-autoscaler
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cluster-autoscaler
  template:
    spec:
      containers:
      - image: k8s.gcr.io/autoscaling/cluster-autoscaler:v1.28.0
        name: cluster-autoscaler
        resources:
          limits:
            cpu: 100m
            memory: 300Mi
          requests:
            cpu: 100m
            memory: 300Mi
        command:
        - ./cluster-autoscaler
        - --v=4
        - --stderrthreshold=info
        - --cloud-provider=aws
        - --skip-nodes-with-local-storage=false
        - --expander=least-waste
        - --node-group-auto-discovery=asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/deha-production
```

## Disaster Recovery

### Backup Strategy

#### 1. Model Backup
```bash
#!/bin/bash
# backup-models.sh

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_PATH="/backups/models/$BACKUP_DATE"

# Create backup directory
mkdir -p $BACKUP_PATH

# Backup model files
aws s3 sync s3://deha-models/ $BACKUP_PATH/models/

# Backup configuration
kubectl get configmaps -n deha-production -o yaml > $BACKUP_PATH/configmaps.yaml
kubectl get secrets -n deha-production -o yaml > $BACKUP_PATH/secrets.yaml

# Backup database
pg_dump -h postgres-host -U postgres deha_metadata > $BACKUP_PATH/metadata.sql

# Upload to backup storage
aws s3 sync $BACKUP_PATH/ s3://deha-backups/$BACKUP_DATE/

echo "Backup completed: $BACKUP_DATE"
```

#### 2. Automated Backup CronJob
```yaml
# backup-cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: deha-backup
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: deha/backup-tool:latest
            command:
            - /bin/bash
            - /scripts/backup-models.sh
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-scripts
          restartPolicy: OnFailure
```

### Recovery Procedures

#### 1. Model Recovery Script
```bash
#!/bin/bash
# recover-models.sh

BACKUP_DATE=$1
if [ -z "$BACKUP_DATE" ]; then
    echo "Usage: $0 <backup_date>"
    exit 1
fi

BACKUP_PATH="/backups/models/$BACKUP_DATE"

# Download backup
aws s3 sync s3://deha-backups/$BACKUP_DATE/ $BACKUP_PATH/

# Restore models
aws s3 sync $BACKUP_PATH/models/ s3://deha-models/

# Restore configuration
kubectl apply -f $BACKUP_PATH/configmaps.yaml
kubectl apply -f $BACKUP_PATH/secrets.yaml

# Restore database
psql -h postgres-host -U postgres -d deha_metadata < $BACKUP_PATH/metadata.sql

# Restart services
kubectl rollout restart deployment/deha-model-service -n deha-production

echo "Recovery completed from backup: $BACKUP_DATE"
```

## Operational Procedures

### Health Checks

#### 1. Application Health Check
```python
# health.py
from fastapi import FastAPI, HTTPException
from typing import Dict, Any
import psutil
import torch

app = FastAPI()

@app.get("/health")
async def health_check() -> Dict[str, Any]:
    """Comprehensive health check endpoint."""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "checks": {}
    }
    
    # Check GPU availability
    try:
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_memory = torch.cuda.get_device_properties(0).total_memory
            health_status["checks"]["gpu"] = {
                "status": "healthy",
                "count": gpu_count,
                "memory_gb": gpu_memory // (1024**3)
            }
        else:
            health_status["checks"]["gpu"] = {"status": "unavailable"}
    except Exception as e:
        health_status["checks"]["gpu"] = {"status": "error", "error": str(e)}
    
    # Check memory usage
    memory = psutil.virtual_memory()
    health_status["checks"]["memory"] = {
        "status": "healthy" if memory.percent < 90 else "warning",
        "usage_percent": memory.percent,
        "available_gb": memory.available // (1024**3)
    }
    
    # Check disk usage
    disk = psutil.disk_usage('/')
    health_status["checks"]["disk"] = {
        "status": "healthy" if disk.percent < 85 else "warning",
        "usage_percent": disk.percent,
        "free_gb": disk.free // (1024**3)
    }
    
    # Overall status
    if any(check.get("status") == "error" for check in health_status["checks"].values()):
        health_status["status"] = "unhealthy"
        raise HTTPException(status_code=503, detail=health_status)
    elif any(check.get("status") == "warning" for check in health_status["checks"].values()):
        health_status["status"] = "degraded"
    
    return health_status

@app.get("/ready")
async def readiness_check() -> Dict[str, str]:
    """Readiness check for Kubernetes."""
    # Check if model is loaded
    if not hasattr(app.state, 'model') or app.state.model is None:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    return {"status": "ready"}
```

### Maintenance Procedures

#### 1. Rolling Update Script
```bash
#!/bin/bash
# rolling-update.sh

NEW_IMAGE=$1
if [ -z "$NEW_IMAGE" ]; then
    echo "Usage: $0 <new_image_tag>"
    exit 1
fi

echo "Starting rolling update to $NEW_IMAGE"

# Update deployment
kubectl set image deployment/deha-model-service \
    deha-model=deha/model-service:$NEW_IMAGE \
    -n deha-production

# Wait for rollout to complete
kubectl rollout status deployment/deha-model-service -n deha-production

# Verify deployment
kubectl get pods -n deha-production -l app=deha-model

echo "Rolling update completed successfully"
```

#### 2. Performance Tuning
```bash
#!/bin/bash
# performance-tune.sh

# GPU performance mode
nvidia-smi -pm 1

# Set GPU clocks to maximum
nvidia-smi -ac 1215,1410

# CPU governor to performance
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# Network optimizations
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' >> /etc/sysctl.conf

sysctl -p

echo "Performance tuning completed"
```

This deployment guide provides comprehensive instructions for deploying DEHA models in production environments with enterprise-grade reliability, security, and scalability. Regular updates to deployment procedures ensure alignment with evolving infrastructure requirements and best practices.