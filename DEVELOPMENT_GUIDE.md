# DEHA Development Guide

## Overview

This guide provides comprehensive instructions for contributing to the DEHA (Dynamic Evolutionary Hybrid Architecture) project. It covers development environment setup, coding standards, testing procedures, and contribution workflows aligned with enterprise software development best practices.

## Table of Contents

1. [Development Environment Setup](#development-environment-setup)
2. [Project Structure](#project-structure)
3. [Coding Standards](#coding-standards)
4. [Testing Framework](#testing-framework)
5. [MLOps Pipeline](#mlops-pipeline)
6. [Contribution Workflow](#contribution-workflow)
7. [Code Review Process](#code-review-process)
8. [Documentation Standards](#documentation-standards)
9. [Performance Guidelines](#performance-guidelines)
10. [Security Considerations](#security-considerations)

## Development Environment Setup

### Prerequisites

#### System Requirements
- **Operating System**: Ubuntu 20.04+ / macOS 12+ / Windows 11 with WSL2
- **Python**: 3.11+ (managed via pyenv)
- **CUDA**: 11.8+ for GPU development
- **Docker**: 20.10+ with <PERSON>er Compose
- **Git**: 2.30+ with Git LFS
- **Memory**: 32GB+ RAM (64GB+ recommended)
- **Storage**: 500GB+ SSD

#### Development Tools
```bash
# Install pyenv for Python version management
curl https://pyenv.run | bash

# Install Python 3.11
pyenv install 3.11.7
pyenv global 3.11.7

# Install Poetry for dependency management
curl -sSL https://install.python-poetry.org | python3 -

# Install pre-commit for code quality
pip install pre-commit

# Install Docker and Docker Compose
# Follow official Docker installation guide for your OS
```

### Environment Setup

#### 1. Clone Repository
```bash
git clone https://github.com/your-org/deha.git
cd deha

# Install Git hooks
pre-commit install
```

#### 2. Python Environment
```bash
# Create virtual environment
poetry install

# Activate environment
poetry shell

# Install development dependencies
poetry install --with dev,test,docs
```

#### 3. Configuration
```bash
# Copy environment template
cp .env.template .env

# Edit configuration
vim .env
```

#### 4. Docker Development Environment
```bash
# Start development services
docker-compose -f docker-compose.dev.yml up -d

# Verify services
docker-compose ps
```

### IDE Configuration

#### VS Code Setup
```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": ".venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

#### PyCharm Setup
- Enable Black formatter
- Configure pytest as test runner
- Set up type checking with mypy
- Install recommended plugins: Docker, Kubernetes, Database Tools

## Project Structure

```
deha/
├── src/
│   ├── deha/
│   │   ├── core/              # Core model implementation
│   │   ├── memory/            # Memory subsystem
│   │   ├── learning/          # Continuous learning
│   │   ├── api/               # API endpoints
│   │   ├── utils/             # Utility functions
│   │   └── config/            # Configuration management
│   └── tests/
│       ├── unit/              # Unit tests
│       ├── integration/       # Integration tests
│       ├── performance/       # Performance tests
│       └── fixtures/          # Test fixtures
├── docs/
│   ├── api/                   # API documentation
│   ├── architecture/          # Architecture docs
│   └── tutorials/             # User tutorials
├── scripts/
│   ├── setup/                 # Setup scripts
│   ├── deployment/            # Deployment scripts
│   └── maintenance/           # Maintenance scripts
├── configs/
│   ├── model/                 # Model configurations
│   ├── training/              # Training configurations
│   └── deployment/            # Deployment configurations
├── docker/
│   ├── Dockerfile.dev         # Development container
│   ├── Dockerfile.prod        # Production container
│   └── docker-compose.yml     # Service orchestration
├── k8s/                       # Kubernetes manifests
├── .github/
│   ├── workflows/             # CI/CD workflows
│   └── ISSUE_TEMPLATE/        # Issue templates
├── pyproject.toml             # Project configuration
├── poetry.lock                # Dependency lock file
└── README.md                  # Project overview
```

## Coding Standards

### Python Style Guide

#### PEP 8 Compliance
- Line length: 88 characters (Black default)
- Indentation: 4 spaces
- Import organization: isort with Black profile
- Docstring format: Google style

#### Type Annotations
```python
from typing import Dict, List, Optional, Union
from pydantic import BaseModel

class ModelConfig(BaseModel):
    """Configuration for DEHA model."""
    
    model_name: str
    parameters: Dict[str, Union[int, float, str]]
    max_tokens: int = 4096
    temperature: float = 0.7
    
    def validate_config(self) -> bool:
        """Validate configuration parameters.
        
        Returns:
            bool: True if configuration is valid.
            
        Raises:
            ValueError: If configuration is invalid.
        """
        if self.temperature < 0.0 or self.temperature > 2.0:
            raise ValueError("Temperature must be between 0.0 and 2.0")
        return True
```

#### Error Handling
```python
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class DEHAError(Exception):
    """Base exception for DEHA-related errors."""
    pass

class ModelLoadError(DEHAError):
    """Raised when model loading fails."""
    pass

def load_model(model_path: str) -> Optional[Model]:
    """Load DEHA model from path.
    
    Args:
        model_path: Path to model files.
        
    Returns:
        Loaded model instance or None if loading fails.
        
    Raises:
        ModelLoadError: If model cannot be loaded.
    """
    try:
        model = Model.from_pretrained(model_path)
        logger.info(f"Successfully loaded model from {model_path}")
        return model
    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        raise ModelLoadError(f"Cannot load model from {model_path}") from e
```

### Code Quality Tools

#### Pre-commit Configuration
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      
  - repo: https://github.com/psf/black
    rev: 23.1.0
    hooks:
      - id: black
      
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
      
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
      
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.0.1
    hooks:
      - id: mypy
```

#### Linting Configuration
```ini
# setup.cfg
[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = .git,__pycache__,docs/source/conf.py,old,build,dist

[mypy]
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
```

## Testing Framework

### Test Structure

#### Unit Tests
```python
import pytest
from unittest.mock import Mock, patch
from deha.core.model import DEHAModel
from deha.memory.vector_store import VectorStore

class TestDEHAModel:
    """Test suite for DEHA model core functionality."""
    
    @pytest.fixture
    def model_config(self):
        """Provide test model configuration."""
        return {
            "model_name": "deha-test",
            "max_tokens": 1024,
            "temperature": 0.7
        }
    
    @pytest.fixture
    def mock_vector_store(self):
        """Mock vector store for testing."""
        mock_store = Mock(spec=VectorStore)
        mock_store.search.return_value = [
            {"content": "test content", "score": 0.95}
        ]
        return mock_store
    
    def test_model_initialization(self, model_config):
        """Test model initialization with valid config."""
        model = DEHAModel(config=model_config)
        assert model.config.model_name == "deha-test"
        assert model.config.max_tokens == 1024
    
    @patch('deha.core.model.VectorStore')
    def test_model_inference(self, mock_vector_store_class, model_config):
        """Test model inference with mocked dependencies."""
        mock_vector_store_class.return_value = mock_vector_store
        
        model = DEHAModel(config=model_config)
        response = model.generate("Test query")
        
        assert response is not None
        assert len(response.content) > 0
        mock_vector_store.search.assert_called_once()
```

#### Integration Tests
```python
import pytest
import asyncio
from deha.api.main import app
from fastapi.testclient import TestClient

class TestAPIIntegration:
    """Integration tests for DEHA API."""
    
    @pytest.fixture
    def client(self):
        """Test client for API testing."""
        return TestClient(app)
    
    def test_health_check(self, client):
        """Test API health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
    
    def test_model_inference_endpoint(self, client):
        """Test model inference API endpoint."""
        payload = {
            "messages": [{"role": "user", "content": "Hello, DEHA!"}],
            "model": "deha-7b",
            "max_tokens": 100
        }
        
        response = client.post("/v1/chat/completions", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert "choices" in data
        assert len(data["choices"]) > 0
```

#### Performance Tests
```python
import pytest
import time
from concurrent.futures import ThreadPoolExecutor
from deha.core.model import DEHAModel

class TestPerformance:
    """Performance tests for DEHA components."""
    
    @pytest.fixture
    def model(self):
        """Initialize model for performance testing."""
        return DEHAModel.from_pretrained("deha-7b")
    
    def test_inference_latency(self, model):
        """Test single inference latency."""
        query = "What is artificial intelligence?"
        
        start_time = time.time()
        response = model.generate(query)
        end_time = time.time()
        
        latency = end_time - start_time
        assert latency < 0.1  # 100ms SLA
        assert len(response.content) > 0
    
    def test_concurrent_inference(self, model):
        """Test concurrent inference throughput."""
        queries = ["Test query " + str(i) for i in range(100)]
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(model.generate, query) for query in queries]
            responses = [future.result() for future in futures]
        end_time = time.time()
        
        total_time = end_time - start_time
        throughput = len(queries) / total_time
        
        assert throughput > 50  # 50 QPS minimum
        assert all(len(r.content) > 0 for r in responses)
```

### Test Execution

#### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src/deha --cov-report=html

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/performance/

# Run tests in parallel
pytest -n auto

# Run tests with verbose output
pytest -v
```

#### Continuous Testing
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        pip install poetry
        poetry install --with dev,test
    
    - name: Run tests
      run: |
        poetry run pytest --cov=src/deha --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

## MLOps Pipeline

### Model Development Lifecycle

#### 1. Experimentation Phase
```python
# experiments/experiment_config.py
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class ExperimentConfig:
    """Configuration for model experiments."""
    
    experiment_name: str
    model_architecture: str
    hyperparameters: Dict[str, Any]
    dataset_version: str
    metrics_to_track: list[str]
    
    def to_mlflow_params(self) -> Dict[str, Any]:
        """Convert to MLflow parameters."""
        return {
            "architecture": self.model_architecture,
            "dataset_version": self.dataset_version,
            **self.hyperparameters
        }
```

#### 2. Training Pipeline
```python
import mlflow
import mlflow.pytorch
from deha.training.trainer import DEHATrainer

def train_model(config: ExperimentConfig) -> str:
    """Train DEHA model with experiment tracking.
    
    Args:
        config: Experiment configuration.
        
    Returns:
        Model run ID for tracking.
    """
    with mlflow.start_run(run_name=config.experiment_name):
        # Log parameters
        mlflow.log_params(config.to_mlflow_params())
        
        # Initialize trainer
        trainer = DEHATrainer(config)
        
        # Train model
        model, metrics = trainer.train()
        
        # Log metrics
        for metric_name, value in metrics.items():
            mlflow.log_metric(metric_name, value)
        
        # Log model
        mlflow.pytorch.log_model(
            model, 
            "model",
            registered_model_name=f"deha-{config.model_architecture}"
        )
        
        return mlflow.active_run().info.run_id
```

#### 3. Model Validation
```python
from deha.validation.evaluator import ModelEvaluator
from deha.validation.benchmarks import run_benchmarks

def validate_model(model_uri: str) -> Dict[str, float]:
    """Validate model performance on benchmarks.
    
    Args:
        model_uri: MLflow model URI.
        
    Returns:
        Validation metrics.
    """
    # Load model
    model = mlflow.pytorch.load_model(model_uri)
    
    # Run evaluations
    evaluator = ModelEvaluator()
    metrics = {}
    
    # Standard benchmarks
    metrics.update(run_benchmarks(model))
    
    # Custom evaluations
    metrics.update(evaluator.evaluate_safety(model))
    metrics.update(evaluator.evaluate_bias(model))
    metrics.update(evaluator.evaluate_robustness(model))
    
    return metrics
```

#### 4. Model Registry
```python
from mlflow.tracking import MlflowClient

def promote_model(model_name: str, version: str, stage: str):
    """Promote model to specified stage.
    
    Args:
        model_name: Registered model name.
        version: Model version to promote.
        stage: Target stage (Staging/Production).
    """
    client = MlflowClient()
    
    # Transition model version
    client.transition_model_version_stage(
        name=model_name,
        version=version,
        stage=stage,
        archive_existing_versions=True
    )
    
    # Add description
    client.update_model_version(
        name=model_name,
        version=version,
        description=f"Promoted to {stage} on {datetime.now()}"
    )
```

### Deployment Pipeline

#### 1. Model Packaging
```dockerfile
# docker/Dockerfile.model
FROM nvidia/cuda:11.8-runtime-ubuntu20.04

# Install Python and dependencies
RUN apt-get update && apt-get install -y python3.11 python3-pip
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy model artifacts
COPY models/ /app/models/
COPY src/ /app/src/

# Set up serving
WORKDIR /app
EXPOSE 8000
CMD ["python", "-m", "deha.serving.main"]
```

#### 2. Kubernetes Deployment
```yaml
# k8s/model-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: deha-model-service
  labels:
    app: deha-model
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: deha-model
  template:
    metadata:
      labels:
        app: deha-model
        version: v1.0.0
    spec:
      containers:
      - name: deha-model
        image: deha/model-service:latest
        ports:
        - containerPort: 8000
        env:
        - name: MODEL_PATH
          value: "/app/models/deha-7b"
        - name: CUDA_VISIBLE_DEVICES
          value: "0"
        resources:
          requests:
            nvidia.com/gpu: 1
            memory: "16Gi"
            cpu: "4"
          limits:
            nvidia.com/gpu: 1
            memory: "32Gi"
            cpu: "8"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### 3. Monitoring and Alerting
```python
from prometheus_client import Counter, Histogram, Gauge
import time

# Metrics
REQUEST_COUNT = Counter('deha_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_LATENCY = Histogram('deha_request_duration_seconds', 'Request latency')
MODEL_ACCURACY = Gauge('deha_model_accuracy', 'Current model accuracy')

def monitor_inference(func):
    """Decorator to monitor inference requests."""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            REQUEST_COUNT.labels(method='POST', endpoint='/inference').inc()
            return result
        finally:
            REQUEST_LATENCY.observe(time.time() - start_time)
    
    return wrapper
```

## Contribution Workflow

### Git Workflow

#### Branch Strategy
- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: Feature development branches
- `hotfix/*`: Critical bug fixes
- `release/*`: Release preparation branches

#### Commit Standards
```bash
# Conventional Commits format
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]

# Examples
feat(core): add continuous learning capability
fix(api): resolve memory leak in inference endpoint
docs(readme): update installation instructions
test(unit): add tests for vector store operations
```

#### Pull Request Process
1. Create feature branch from `develop`
2. Implement changes with tests
3. Update documentation
4. Run pre-commit hooks
5. Submit pull request with detailed description
6. Address code review feedback
7. Merge after approval

### Code Review Guidelines

#### Review Checklist
- [ ] Code follows style guidelines
- [ ] Tests cover new functionality
- [ ] Documentation is updated
- [ ] Performance impact is considered
- [ ] Security implications are reviewed
- [ ] Breaking changes are documented

#### Review Process
1. **Automated Checks**: CI/CD pipeline validation
2. **Peer Review**: At least one team member approval
3. **Architecture Review**: For significant changes
4. **Security Review**: For security-sensitive changes

## Documentation Standards

### Code Documentation

#### Docstring Format
```python
def generate_response(
    self, 
    query: str, 
    context: Optional[str] = None,
    max_tokens: int = 1024
) -> ModelResponse:
    """Generate response using DEHA model.
    
    This method processes the input query, retrieves relevant context
    from memory systems, and generates a coherent response using the
    DEHA architecture.
    
    Args:
        query: Input query string.
        context: Optional context for response generation.
        max_tokens: Maximum number of tokens to generate.
        
    Returns:
        ModelResponse containing generated text and metadata.
        
    Raises:
        ValueError: If query is empty or invalid.
        ModelError: If model inference fails.
        
    Example:
        >>> model = DEHAModel.from_pretrained("deha-7b")
        >>> response = model.generate_response("What is AI?")
        >>> print(response.content)
        "Artificial Intelligence (AI) refers to..."
    """
```

#### API Documentation
```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

app = FastAPI(
    title="DEHA API",
    description="Dynamic Evolutionary Hybrid Architecture API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

class ChatRequest(BaseModel):
    """Request model for chat completions.
    
    Attributes:
        messages: List of conversation messages.
        model: Model name to use for generation.
        temperature: Sampling temperature (0.0-2.0).
        max_tokens: Maximum tokens to generate.
    """
    messages: List[Dict[str, str]]
    model: str = "deha-7b"
    temperature: float = 0.7
    max_tokens: int = 1024

@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatRequest):
    """Create a chat completion.
    
    Generate a response to a conversation using the DEHA model.
    Supports multi-turn conversations with context awareness.
    
    Args:
        request: Chat completion request parameters.
        
    Returns:
        Chat completion response with generated message.
        
    Raises:
        HTTPException: If request is invalid or processing fails.
    """
```

## Performance Guidelines

### Optimization Strategies

#### Memory Management
```python
import gc
import torch
from contextlib import contextmanager

@contextmanager
def memory_efficient_inference():
    """Context manager for memory-efficient inference."""
    try:
        # Clear cache before inference
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        yield
        
    finally:
        # Clean up after inference
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()

# Usage
with memory_efficient_inference():
    response = model.generate(query)
```

#### Caching Strategy
```python
from functools import lru_cache
import redis
import pickle

class ResponseCache:
    """Redis-based response caching."""
    
    def __init__(self, redis_url: str, ttl: int = 3600):
        self.redis_client = redis.from_url(redis_url)
        self.ttl = ttl
    
    def get(self, key: str) -> Optional[Any]:
        """Get cached response."""
        data = self.redis_client.get(key)
        return pickle.loads(data) if data else None
    
    def set(self, key: str, value: Any) -> None:
        """Cache response with TTL."""
        self.redis_client.setex(
            key, 
            self.ttl, 
            pickle.dumps(value)
        )

# Usage with decorator
def cached_inference(cache: ResponseCache):
    def decorator(func):
        def wrapper(query: str, *args, **kwargs):
            cache_key = f"inference:{hash(query)}"
            
            # Try cache first
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result
            
            # Generate and cache
            result = func(query, *args, **kwargs)
            cache.set(cache_key, result)
            return result
        
        return wrapper
    return decorator
```

#### Profiling and Monitoring
```python
import cProfile
import pstats
from functools import wraps

def profile_performance(func):
    """Decorator to profile function performance."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        profiler = cProfile.Profile()
        profiler.enable()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            profiler.disable()
            stats = pstats.Stats(profiler)
            stats.sort_stats('cumulative')
            stats.print_stats(10)  # Top 10 functions
    
    return wrapper

# Usage
@profile_performance
def expensive_operation():
    # Your code here
    pass
```

## Security Considerations

### Secure Coding Practices

#### Input Validation
```python
from pydantic import BaseModel, validator
import re

class SecureInput(BaseModel):
    """Secure input validation model."""
    
    query: str
    user_id: str
    
    @validator('query')
    def validate_query(cls, v):
        """Validate query input."""
        if not v or len(v.strip()) == 0:
            raise ValueError('Query cannot be empty')
        
        if len(v) > 10000:
            raise ValueError('Query too long')
        
        # Check for potential injection attempts
        dangerous_patterns = [
            r'<script.*?>',
            r'javascript:',
            r'on\w+\s*=',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, v, re.IGNORECASE):
                raise ValueError('Potentially dangerous input detected')
        
        return v
    
    @validator('user_id')
    def validate_user_id(cls, v):
        """Validate user ID format."""
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Invalid user ID format')
        return v
```

#### Secrets Management
```python
import os
from cryptography.fernet import Fernet

class SecretManager:
    """Secure secrets management."""
    
    def __init__(self):
        self.cipher_suite = Fernet(os.environ['ENCRYPTION_KEY'].encode())
    
    def encrypt_secret(self, secret: str) -> str:
        """Encrypt a secret value."""
        return self.cipher_suite.encrypt(secret.encode()).decode()
    
    def decrypt_secret(self, encrypted_secret: str) -> str:
        """Decrypt a secret value."""
        return self.cipher_suite.decrypt(encrypted_secret.encode()).decode()
    
    @classmethod
    def get_secret(cls, secret_name: str) -> str:
        """Get secret from environment or secret store."""
        # Try environment first
        secret = os.environ.get(secret_name)
        if secret:
            return secret
        
        # Try secret store (AWS Secrets Manager, etc.)
        # Implementation depends on your secret store
        raise ValueError(f"Secret {secret_name} not found")
```

### Security Testing
```python
import pytest
from deha.security.validator import InputValidator

class TestSecurity:
    """Security-focused test cases."""
    
    def test_sql_injection_prevention(self):
        """Test SQL injection prevention."""
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'/**/OR/**/1=1--"
        ]
        
        validator = InputValidator()
        for malicious_input in malicious_inputs:
            with pytest.raises(ValueError):
                validator.validate_query(malicious_input)
    
    def test_xss_prevention(self):
        """Test XSS prevention."""
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>"
        ]
        
        validator = InputValidator()
        for payload in xss_payloads:
            with pytest.raises(ValueError):
                validator.validate_query(payload)
```

This development guide provides a comprehensive framework for contributing to the DEHA project while maintaining high standards of code quality, security, and performance. Regular updates to this guide ensure alignment with evolving best practices and project requirements.