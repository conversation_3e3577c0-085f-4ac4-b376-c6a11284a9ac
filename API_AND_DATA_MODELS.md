# DEHA API Specifications and Data Models

## 1. API Design Principles

### RESTful Architecture Standards
- **Protocol**: HTTPS-only communication with TLS 1.3 encryption
- **Versioning**: Semantic versioning with `/api/v1/`, `/api/v2/` namespace structure
- **Content Type**: JSON for data exchange with UTF-8 encoding
- **Authentication**: OAuth 2.0 with JWT tokens and refresh token rotation
- **Rate Limiting**: Token bucket algorithm with tiered limits based on subscription

### Response Format Standardization
All API responses follow a consistent envelope format:
```json
{
  "success": boolean,
  "data": object | array | null,
  "error": {
    "code": string,
    "message": string,
    "details": object
  } | null,
  "metadata": {
    "timestamp": "ISO 8601 datetime",
    "request_id": "UUID",
    "version": "API version",
    "rate_limit": {
      "remaining": number,
      "reset_time": "ISO 8601 datetime"
    }
  }
}
```

### HTTP Status Code Standards
- **2xx Success**: 200 (OK), 201 (Created), 202 (Accepted), 204 (No Content)
- **4xx Client Error**: 400 (Bad Request), 401 (Unauthorized), 403 (Forbidden), 404 (Not Found), 429 (Too Many Requests)
- **5xx Server Error**: 500 (Internal Server Error), 502 (Bad Gateway), 503 (Service Unavailable)

## 2. Ana Veri Modelleri (Pydantic Stili)

### `InputObject`
Algı katmanından gelen standartlaştırılmış girdi
```json
{
  "source": "cli",
  "user_id": "user_123456",
  "timestamp": "2025-08-05T12:00:00Z",
  "content": "Python için en iyi web framework hangisi?",
  "metadata": {
    "session_id": "sess_123456789",
    "command_id": "cmd_987654321"
  }
}
```

### `ProcessedInput`
İşleme katmanından çıkan, zenginleştirilmiş girdi
```json
{
  "input_obj": {
    "source": "cli",
    "user_id": "user_123456",
    "timestamp": "2025-08-05T12:00:00Z",
    "content": "Python için en iyi web framework hangisi?",
    "metadata": {
      "session_id": "sess_123456789",
      "command_id": "cmd_987654321"
    }
  },
  "intent": "framework_comparison",
  "entities": [
    {"type": "programming_language", "value": "Python"},
    {"type": "framework", "value": "web framework"}
  ],
  "reliability_score": 0.95
}
```

### `Plan`
Bilişsel Motorun ürettiği eylem planı
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "steps": [
    {
      "step_id": 1,
      "tool": "memory_query",
      "params": {
        "query_text": "Python web frameworks comparison",
        "type": "vector"
      },
      "dependencies": []
    },
    {
      "step_id": 2,
      "tool": "web_search",
      "params": {
        "query": "Python web frameworks comparison 2025"
      },
      "dependencies": [1]
    }
  ],
  "created_at": "2025-08-05T12:00:05Z"
}
```

### `TaskResult`
Bir görevin sonucunu temsil eder
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "result": "FastAPI ve Django karşılaştırması yapıldı...",
  "execution_time": 2.5,
  "feedback_requested": true
}
```

### `LearningRequest`
Öğrenme süreci için talep
```json
{
  "content": "FastAPI, Django'dan daha hızlı geliştirme sunar ve modern Python özelliklerini destekler.",
  "source_task_id": "550e8400-e29b-41d4-a716-446655440000",
  "feedback": "positive",
  "confidence": 0.9,
  "metadata": {
    "context": "web_framework_comparison",
    "timestamp": "2025-08-05T12:00:10Z"
  }
}
```

## 3. Endpoint Spesifikasyonları

### 3.1. `POST /api/v1/process`
- **Açıklama:** Algı Katmanından gelen ham girdiyi alır ve Bilişsel Motora iletmek üzere işler.
- **Request Body:** `InputObject`
- **Success Response (200 OK):** 
```json
{
  "success": true,
  "data": {
    "processed_input": {
      "input_obj": {...},
      "intent": "framework_comparison",
      "entities": [...],
      "reliability_score": 0.95
    }
  },
  "error": null
}
```

### 3.2. `POST /api/v1/query-memory`
- **Açıklama:** Bilişsel Motorun Hibrit Bellekten bilgi sorgulamasını sağlar.
- **Request Body:** 
```json
{
  "query_text": "Python web frameworks",
  "type": "vector",
  "limit": 10
}
```
- **Success Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "vec_12345",
        "content": "FastAPI, modern ve hızlı (yüksek performanslı) bir web framework'tür...",
        "score": 0.92,
        "metadata": {
          "source": "documentation",
          "timestamp": "2025-08-05T10:00:00Z"
        }
      }
    ]
  },
  "error": null
}
```

### 3.3. `POST /api/v1/execute-plan`
- **Açıklama:** Bilişsel Motor tarafından oluşturulan planı Aksiyon Katmanında yürütür.
- **Request Body:** `Plan`
- **Success Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "task_result": {
      "task_id": "550e8400-e29b-41d4-a716-446655440000",
      "status": "completed",
      "result": "Görev başarıyla tamamlandı",
      "execution_time": 1.8
    }
  },
  "error": null
}
```

### 3.4. `POST /api/v1/learn`
- **Açıklama:** Bir etkileşim sonucunda öğrenilecek yeni bilgiyi Hibrit Belleğe kaydeder.
- **Request Body:** `LearningRequest`
- **Success Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "status": "learned",
    "memory_id": "mem_789012",
    "stored_in": ["vector_db", "knowledge_graph"]
  },
  "error": null
}
```

### 3.5. `POST /api/v1/generate-response`
- **Açıklama:** Görev sonucunu kullanıcıya sunulabilecek formatta metne dönüştürür.
- **Request Body:** 
```json
{
  "task_result": {...},
  "response_format": "detailed_comparison"
}
```
- **Success Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "response_text": "FastAPI ve Django karşılaştırması:\n\n1. Performans: FastAPI daha hızlıdır...",
    "suggested_followups": ["Diğer framework'leri de karşılaştır", "FastAPI kullanım örnekleri"]
  },
  "error": null
}
```

### 3.6. `GET /api/v1/health`
- **Açıklama:** Sistemin genel sağlık durumunu kontrol eder.
- **Success Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "components": {
      "perception": "online",
      "processing": "online",
      "memory": "online",
      "cognitive_engine": "online",
      "action": "online",
      "security": "online"
    },
    "timestamp": "2025-08-05T12:00:00Z"
  },
  "error": null
}
